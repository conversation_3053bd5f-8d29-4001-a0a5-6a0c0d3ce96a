"""
预测器模块

包含各种权重重要性预测算法：
1. 增强预测器（默认）
2. Wanda预测器
3. SNIP预测器
"""

import torch


class PredictorRegistry:
    """预测器注册表，管理所有可用的预测器"""
    
    @staticmethod
    def dolphin_predictor(activation_stats, weight_matrix, layer_info):
        """
        增强多特征融合预测器 - 基于最新研究优化

        核心思想：
        1. 多维激活特征融合：L2范数、方差、熵、偏度等
        2. 权重结构分析：相对重要性、分布特性
        3. 层级自适应：根据层类型和深度调整策略
        4. 数值稳定性：鲁棒的归一化和温度调节

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）

        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取丰富的激活统计特征
        l2_norm = activation_stats['l2_norm']      # [in_features] - 激活幅度
        variance = activation_stats['var']         # [in_features] - 激活变异性
        mean_abs = torch.abs(activation_stats['mean'])  # [in_features] - 激活偏移
        skewness = torch.abs(activation_stats['skewness'])  # [in_features] - 分布不对称性
        entropy = activation_stats['entropy']      # [in_features] - 信息丰富度
        max_val = activation_stats['max_val']      # [in_features] - 动态范围上界
        min_val = activation_stats['min_val']      # [in_features] - 动态范围下界

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]

        # === 1. 基础Wanda分量：权重×激活幅度 ===
        activation_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        wanda_component = weight_abs * activation_scale

        # === 2. 信息理论分量：权重×熵 ===
        # 高熵表示激活包含更多信息，对应权重更重要
        entropy_scale = torch.sqrt(entropy + 1e-8).unsqueeze(0)
        entropy_component = weight_abs * entropy_scale

        # === 3. 敏感性分量：权重×(方差×偏度) ===
        # 高方差+高偏度表示激活对输入变化敏感
        sensitivity = variance * (1 + skewness)
        sensitivity_scale = torch.sqrt(sensitivity + 1e-8).unsqueeze(0)
        sensitivity_component = weight_abs * sensitivity_scale

        # === 4. 动态范围分量：权重×激活范围 ===
        # 大动态范围表示激活能表达更丰富的信息
        dynamic_range = torch.clamp(max_val - min_val, min=1e-8)
        range_scale = torch.sqrt(dynamic_range + 1e-8).unsqueeze(0)
        range_component = weight_abs * range_scale

        # === 5. 权重结构分析 ===
        # 5.1 相对重要性：权重相对于同一输出神经元的其他权重
        weight_mean = torch.mean(weight_abs, dim=1, keepdim=True)
        weight_std = torch.std(weight_abs, dim=1, keepdim=True)
        relative_importance = (weight_abs - weight_mean) / (weight_std + 1e-8)

        # 5.2 权重分布特性：基于权重在输入维度上的分布
        input_weight_mean = torch.mean(weight_abs, dim=0, keepdim=True)
        input_weight_std = torch.std(weight_abs, dim=0, keepdim=True)
        input_relative = (weight_abs - input_weight_mean) / (input_weight_std + 1e-8)
        
        # === 6. 层级自适应权重分配 ===
        layer_id = layer_info.get('layer_id', 0)
        layer_name = layer_info.get('layer_name', '')

        # 根据层类型调整各分量权重 - 基于最新研究优化
        if 'q_proj' in layer_name:
            # Query投影：注意力核心，重视信息理论和敏感性
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.25, 0.30, 0.25, 0.10, 0.10
        elif 'k_proj' in layer_name:
            # Key投影：与Query协同，平衡各项特征
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.25, 0.25, 0.25, 0.15, 0.10
        elif 'v_proj' in layer_name:
            # Value投影：信息载体，重视动态范围和熵
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.20, 0.30, 0.20, 0.20, 0.10
        elif 'o_proj' in layer_name:
            # Output投影：整合输出，重视权重结构
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.20, 0.20, 0.20, 0.15, 0.25
        elif 'gate_proj' in layer_name:
            # Gate投影：FFN门控，重视敏感性（控制信息流）
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.20, 0.25, 0.35, 0.10, 0.10
        elif 'up_proj' in layer_name:
            # Up投影：FFN扩展，平衡处理
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.25, 0.25, 0.25, 0.15, 0.10
        elif 'down_proj' in layer_name:
            # Down投影：FFN压缩，重视权重结构（可以更激进剪枝）
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.15, 0.20, 0.20, 0.15, 0.30
        else:
            # 默认权重分配：平衡各项特征
            w_wanda, w_entropy, w_sens, w_range, w_rel = 0.25, 0.25, 0.25, 0.15, 0.10

        # === 7. 分量归一化和融合 ===
        # 使用稳健的归一化方法（避免极值影响）
        def robust_normalize(tensor):
            # 使用99%分位数作为归一化基准，避免极值影响
            percentile_99 = torch.quantile(tensor.flatten(), 0.99)
            return tensor / (percentile_99 + 1e-8)

        wanda_norm = robust_normalize(wanda_component)
        entropy_norm = robust_normalize(entropy_component)
        sens_norm = robust_normalize(sensitivity_component)
        range_norm = robust_normalize(range_component)
        rel_norm = robust_normalize(relative_importance)

        # 多特征加权融合
        importance_scores = (w_wanda * wanda_norm +
                           w_entropy * entropy_norm +
                           w_sens * sens_norm +
                           w_range * range_norm +
                           w_rel * rel_norm)
        
        # === 8. 最终分数增强（保持原始动态范围）===
        # 不使用softmax，而是使用更温和的增强方法

        # 8.1 基于层类型的分数调整
        layer_multiplier = 1.0
        if 'q_proj' in layer_name or 'k_proj' in layer_name:
            # 关键注意力层：适度增强
            layer_multiplier = 1.2
        elif 'gate_proj' in layer_name:
            # 门控层：保守增强
            layer_multiplier = 1.0
        elif 'v_proj' in layer_name or 'up_proj' in layer_name:
            # 中等重要性层：标准增强
            layer_multiplier = 1.1
        elif 'o_proj' in layer_name:
            # 输出层：较强增强
            layer_multiplier = 1.3
        elif 'down_proj' in layer_name:
            # 下投影：最强增强（便于剪枝）
            layer_multiplier = 1.5

        # 8.2 基于层深度的调整
        if layer_id < 4:  # 早期层更保守
            layer_multiplier *= 0.8
        elif layer_id > 20:  # 后期层更激进
            layer_multiplier *= 1.2

        # 8.3 应用层级调整
        importance_scores = importance_scores * layer_multiplier

        # 8.4 添加小的随机扰动增加鲁棒性（可选）
        if torch.rand(1).item() > 0.5:  # 50%概率添加扰动
            noise = torch.randn_like(importance_scores) * 0.001
            importance_scores = importance_scores + noise

        # 8.5 确保非负性
        importance_scores = torch.clamp(importance_scores, min=0.0)

        return importance_scores

    @staticmethod
    def wanda_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        增强Wanda预测器 - 基于权重幅度和激活统计的改进版本

        核心改进：
        1. 多尺度激活特征：L2范数 + 方差 + 动态范围
        2. 权重结构感知：考虑权重分布特性
        3. 层级自适应：根据层类型调整权重
        4. 数值稳定性：鲁棒的归一化方法

        Returns:
            importance_scores: 权重重要性分数
        """
        # 获取激活统计特征
        l2_norm = activation_stats['l2_norm']      # [in_features]
        variance = activation_stats['var']         # [in_features]
        max_val = activation_stats['max_val']      # [in_features]
        min_val = activation_stats['min_val']      # [in_features]

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]

        # === 1. 经典Wanda分量：权重×L2范数 ===
        l2_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        l2_component = weight_abs * l2_scale

        # === 2. 方差分量：权重×方差 ===
        # 高方差表示激活变化大，对应权重更敏感
        var_scale = torch.sqrt(variance + 1e-8).unsqueeze(0)
        var_component = weight_abs * var_scale

        # === 3. 动态范围分量：权重×激活范围 ===
        # 大动态范围表示激活能表达更丰富的信息
        dynamic_range = torch.clamp(max_val - min_val, min=1e-8)
        range_scale = torch.sqrt(dynamic_range).unsqueeze(0)
        range_component = weight_abs * range_scale

        # === 4. 权重结构感知 ===
        # 考虑权重在输出维度上的相对重要性
        weight_mean_out = torch.mean(weight_abs, dim=1, keepdim=True)
        weight_relative = weight_abs / (weight_mean_out + 1e-8)

        # === 5. 层级自适应权重分配 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '')
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 注意力层：重视L2范数和方差
                w_l2, w_var, w_range, w_struct = 0.4, 0.3, 0.2, 0.1
            elif 'v_proj' in layer_name:
                # Value层：平衡各项特征
                w_l2, w_var, w_range, w_struct = 0.35, 0.25, 0.25, 0.15
            elif 'gate_proj' in layer_name:
                # 门控层：重视方差（敏感性）
                w_l2, w_var, w_range, w_struct = 0.3, 0.4, 0.2, 0.1
            else:
                # 默认权重
                w_l2, w_var, w_range, w_struct = 0.4, 0.3, 0.2, 0.1
        else:
            w_l2, w_var, w_range, w_struct = 0.4, 0.3, 0.2, 0.1

        # === 6. 分量融合 ===
        # 使用稳健归一化
        def robust_normalize(tensor):
            percentile_95 = torch.quantile(tensor.flatten(), 0.95)
            return tensor / (percentile_95 + 1e-8)

        l2_norm_comp = robust_normalize(l2_component)
        var_norm_comp = robust_normalize(var_component)
        range_norm_comp = robust_normalize(range_component)
        struct_norm_comp = robust_normalize(weight_relative)

        importance_scores = (w_l2 * l2_norm_comp +
                           w_var * var_norm_comp +
                           w_range * range_norm_comp +
                           w_struct * struct_norm_comp)

        return importance_scores
    

    @staticmethod
    def snip_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        增强SNIP预测器 - 基于连接敏感性的改进版本

        核心改进：
        1. 多维梯度近似：方差 + 偏度 + 熵
        2. 权重敏感性分析：考虑权重对输出的影响
        3. 连接重要性评估：基于信息理论的连接评分
        4. 层级自适应：根据层类型调整策略

        Returns:
            importance_scores: 权重重要性分数
        """
        # 获取激活统计特征
        variance = activation_stats['var']         # [in_features] - 梯度近似主要项
        skewness = torch.abs(activation_stats['skewness'])  # [in_features] - 分布不对称性
        entropy = activation_stats['entropy']      # [in_features] - 信息丰富度
        mean_abs = torch.abs(activation_stats['mean'])  # [in_features] - 激活偏移

        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]

        # === 1. 经典SNIP分量：权重×方差（梯度近似）===
        grad_approx = variance.unsqueeze(0)
        snip_component = weight_abs * grad_approx

        # === 2. 高阶梯度近似：权重×(方差×偏度) ===
        # 偏度反映激活分布的不对称性，高偏度表示梯度变化更剧烈
        higher_order_grad = variance * (1 + skewness)
        higher_grad_scale = higher_order_grad.unsqueeze(0)
        higher_component = weight_abs * higher_grad_scale

        # === 3. 信息敏感性：权重×熵 ===
        # 高熵表示激活包含更多信息，对应连接更重要
        info_scale = entropy.unsqueeze(0)
        info_component = weight_abs * info_scale

        # === 4. 激活幅度敏感性：权重×激活均值 ===
        # 考虑激活的基础幅度对梯度的影响
        magnitude_scale = mean_abs.unsqueeze(0)
        magnitude_component = weight_abs * magnitude_scale

        # === 5. 连接结构分析 ===
        # 5.1 输出连接重要性：权重在输出维度的相对重要性
        output_importance = weight_abs / (torch.sum(weight_abs, dim=1, keepdim=True) + 1e-8)

        # 5.2 输入连接重要性：权重在输入维度的相对重要性
        input_importance = weight_abs / (torch.sum(weight_abs, dim=0, keepdim=True) + 1e-8)

        # === 6. 层级自适应权重分配 ===
        if layer_info:
            layer_name = layer_info.get('layer_name', '')
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 注意力层：重视梯度近似和信息敏感性
                w_snip, w_higher, w_info, w_mag, w_struct = 0.3, 0.25, 0.25, 0.1, 0.1
            elif 'gate_proj' in layer_name:
                # 门控层：重视高阶梯度（敏感性）
                w_snip, w_higher, w_info, w_mag, w_struct = 0.25, 0.35, 0.2, 0.1, 0.1
            elif 'down_proj' in layer_name:
                # 下投影：重视连接结构（可以更激进剪枝）
                w_snip, w_higher, w_info, w_mag, w_struct = 0.2, 0.2, 0.2, 0.15, 0.25
            else:
                # 默认权重
                w_snip, w_higher, w_info, w_mag, w_struct = 0.3, 0.25, 0.2, 0.15, 0.1
        else:
            w_snip, w_higher, w_info, w_mag, w_struct = 0.3, 0.25, 0.2, 0.15, 0.1

        # === 7. 分量融合 ===
        # 使用稳健归一化
        def robust_normalize(tensor):
            percentile_90 = torch.quantile(tensor.flatten(), 0.90)
            return tensor / (percentile_90 + 1e-8)

        snip_norm = robust_normalize(snip_component)
        higher_norm = robust_normalize(higher_component)
        info_norm = robust_normalize(info_component)
        mag_norm = robust_normalize(magnitude_component)
        struct_norm = robust_normalize(output_importance * input_importance)

        importance_scores = (w_snip * snip_norm +
                           w_higher * higher_norm +
                           w_info * info_norm +
                           w_mag * mag_norm +
                           w_struct * struct_norm)

        return importance_scores

    @classmethod
    def get_predictor(cls, strategy="dolphin"):
        """
        获取指定策略的预测器

        Args:
            strategy: 预测器策略名称
                - "dolphin": 增强预测器
                - "wanda": Wanda预测器
                - "snip": SNIP预测器

        Returns:
            predictor: 预测器函数
        """
        strategy_map = {
            "dolphin": cls.dolphin_predictor,
            "wanda": cls.wanda_predictor,
            "snip": cls.snip_predictor
        }
        
        if strategy in strategy_map:
            return strategy_map[strategy]
        else:
            available = list(strategy_map.keys())
            raise ValueError(f"未知策略 '{strategy}'，可用策略: {available}")

    @classmethod
    def list_available_strategies(cls):
        """获取所有可用的预测器策略"""
        return ["dolphin", "wanda", "snip"]
